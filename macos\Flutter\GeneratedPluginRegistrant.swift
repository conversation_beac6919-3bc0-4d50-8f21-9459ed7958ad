//
//  Generated file. Do not edit.
//

import FlutterMacOS
import Foundation

import flutter_image_compress_macos
import path_provider_foundation
import printing
import share_plus

func RegisterGeneratedPlugins(registry: FlutterPluginRegistry) {
  FlutterImageCompressMacosPlugin.register(with: registry.registrar(forPlugin: "FlutterImageCompressMacosPlugin"))
  PathProviderPlugin.register(with: registry.registrar(forPlugin: "PathProviderPlugin"))
  PrintingPlugin.register(with: registry.registrar(forPlugin: "PrintingPlugin"))
  SharePlusMacosPlugin.register(with: registry.registrar(forPlugin: "SharePlusMacosPlugin"))
}
