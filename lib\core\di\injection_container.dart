import 'package:get_it/get_it.dart';
import '../../features/camera/data/repositories/camera_repository_impl.dart';
import '../../features/camera/domain/repositories/camera_repository.dart';
import '../../features/camera/domain/usecases/capture_image.dart';
import '../../features/camera/domain/usecases/initialize_camera.dart';
import '../../features/camera/presentation/bloc/camera_bloc.dart';
import '../../features/document_processing/data/repositories/document_processing_repository_impl.dart';
import '../../features/document_processing/domain/repositories/document_processing_repository.dart';
import '../../features/document_processing/domain/usecases/detect_edges.dart';
import '../../features/document_processing/domain/usecases/crop_image.dart';
import '../../features/document_processing/presentation/bloc/document_processing_bloc.dart';
import '../../features/pdf_generation/data/repositories/pdf_repository_impl.dart';
import '../../features/pdf_generation/domain/repositories/pdf_repository.dart';
import '../../features/pdf_generation/domain/usecases/generate_pdf.dart';
import '../../features/pdf_generation/presentation/bloc/pdf_bloc.dart';

final sl = GetIt.instance;

Future<void> init() async {
  //! Features - Camera
  // Bloc
  sl.registerFactory(() => CameraBloc(
    initializeCamera: sl(),
    captureImage: sl(),
  ));

  // Use cases
  sl.registerLazySingleton(() => InitializeCamera(sl()));
  sl.registerLazySingleton(() => CaptureImage(sl()));

  // Repository
  sl.registerLazySingleton<CameraRepository>(
    () => CameraRepositoryImpl(),
  );

  //! Features - Document Processing
  // Bloc
  sl.registerFactory(() => DocumentProcessingBloc(
    detectEdges: sl(),
    cropImage: sl(),
  ));

  // Use cases
  sl.registerLazySingleton(() => DetectEdges(sl()));
  sl.registerLazySingleton(() => CropImage(sl()));

  // Repository
  sl.registerLazySingleton<DocumentProcessingRepository>(
    () => DocumentProcessingRepositoryImpl(),
  );

  //! Features - PDF Generation
  // Bloc
  sl.registerFactory(() => PdfBloc(
    generatePdf: sl(),
  ));

  // Use cases
  sl.registerLazySingleton(() => GeneratePdf(sl()));

  // Repository
  sl.registerLazySingleton<PdfRepository>(
    () => PdfRepositoryImpl(),
  );
}
