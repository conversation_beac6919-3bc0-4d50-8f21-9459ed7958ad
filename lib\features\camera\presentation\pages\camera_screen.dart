import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:camera/camera.dart';
import '../../../../core/di/injection_container.dart';
import '../bloc/camera_bloc.dart';
import '../bloc/camera_event.dart';
import '../bloc/camera_state.dart';
import '../widgets/camera_controls.dart';
import '../widgets/camera_preview_widget.dart';

class CameraScreen extends StatelessWidget {
  const CameraScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => sl<CameraBloc>()..add(InitializeCameraEvent()),
      child: const CameraView(),
    );
  }
}

class CameraView extends StatelessWidget {
  const CameraView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: BlocConsumer<CameraBloc, CameraState>(
        listener: (context, state) {
          if (state is CameraError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
          
          if (state is CameraImageCaptured) {
            // Navigate to document processing screen
            Navigator.of(context).pushNamed(
              '/document-processing',
              arguments: state.imageBytes,
            );
          }
        },
        builder: (context, state) {
          return SafeArea(
            child: Column(
              children: [
                // App Bar
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                        ),
                      ),
                      const Expanded(
                        child: Text(
                          'Scan Document',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      if (state is CameraReady)
                        IconButton(
                          onPressed: () {
                            context.read<CameraBloc>().add(ToggleFlashEvent());
                          },
                          icon: Icon(
                            state.isFlashOn ? Icons.flash_on : Icons.flash_off,
                            color: Colors.white,
                          ),
                        ),
                    ],
                  ),
                ),
                
                // Camera Preview
                Expanded(
                  child: _buildCameraContent(context, state),
                ),
                
                // Camera Controls
                if (state is CameraReady || state is CameraCapturing)
                  CameraControls(
                    onCapture: () {
                      context.read<CameraBloc>().add(CaptureImageEvent());
                    },
                    isCapturing: state is CameraCapturing,
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCameraContent(BuildContext context, CameraState state) {
    if (state is CameraLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }
    
    if (state is CameraReady || state is CameraCapturing) {
      final controller = state is CameraReady 
          ? state.controller 
          : (state as CameraCapturing).controller;
      
      return CameraPreviewWidget(controller: controller);
    }
    
    if (state is CameraError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'Camera Error',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              state.message,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context.read<CameraBloc>().add(InitializeCameraEvent());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
    
    return const Center(
      child: Text(
        'Initializing Camera...',
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
        ),
      ),
    );
  }
}
