import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image/image.dart' as img;
import '../../domain/repositories/document_processing_repository.dart';

class DocumentProcessingRepositoryImpl implements DocumentProcessingRepository {
  @override
  Future<List<Offset>> detectEdges(Uint8List imageBytes) async {
    try {
      // For now, return default corners (will be enhanced with ML Kit later)
      // This is a basic implementation for Phase 1
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      final width = image.width.toDouble();
      final height = image.height.toDouble();

      // Return approximate document corners (10% margin from edges)
      final margin = 0.1;
      return [
        Offset(width * margin, height * margin), // Top-left
        Offset(width * (1 - margin), height * margin), // Top-right
        Offset(width * (1 - margin), height * (1 - margin)), // Bottom-right
        Offset(width * margin, height * (1 - margin)), // Bottom-left
      ];
    } catch (e) {
      throw Exception('Edge detection failed: $e');
    }
  }

  @override
  Future<Uint8List> cropImage(
    Uint8List imageBytes,
    List<Offset> corners,
  ) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // For Phase 1, perform a simple rectangular crop
      // In later phases, this will be enhanced with perspective correction

      // Find bounding rectangle of the corners
      double minX = corners.map((c) => c.dx).reduce((a, b) => a < b ? a : b);
      double maxX = corners.map((c) => c.dx).reduce((a, b) => a > b ? a : b);
      double minY = corners.map((c) => c.dy).reduce((a, b) => a < b ? a : b);
      double maxY = corners.map((c) => c.dy).reduce((a, b) => a > b ? a : b);

      // Ensure coordinates are within image bounds
      minX = minX.clamp(0, image.width.toDouble());
      maxX = maxX.clamp(0, image.width.toDouble());
      minY = minY.clamp(0, image.height.toDouble());
      maxY = maxY.clamp(0, image.height.toDouble());

      final cropWidth = (maxX - minX).toInt();
      final cropHeight = (maxY - minY).toInt();

      if (cropWidth <= 0 || cropHeight <= 0) {
        throw Exception('Invalid crop dimensions');
      }

      // Crop the image
      final croppedImage = img.copyCrop(
        image,
        x: minX.toInt(),
        y: minY.toInt(),
        width: cropWidth,
        height: cropHeight,
      );

      // Encode back to bytes
      final encodedImage = img.encodeJpg(croppedImage, quality: 85);
      return Uint8List.fromList(encodedImage);
    } catch (e) {
      throw Exception('Image cropping failed: $e');
    }
  }

  @override
  Future<Uint8List> enhanceImage(Uint8List imageBytes) async {
    try {
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Apply basic enhancements
      var enhancedImage = image;

      // Increase contrast
      enhancedImage = img.contrast(enhancedImage, contrast: 1.2);

      // Adjust brightness slightly (using adjustColor instead of deprecated brightness)
      enhancedImage = img.adjustColor(enhancedImage, brightness: 1.1);

      // Sharpen the image
      enhancedImage = img.convolution(
        enhancedImage,
        filter: [0, -1, 0, -1, 5, -1, 0, -1, 0],
      );

      // Encode back to bytes
      final encodedImage = img.encodeJpg(enhancedImage, quality: 90);
      return Uint8List.fromList(encodedImage);
    } catch (e) {
      throw Exception('Image enhancement failed: $e');
    }
  }
}
