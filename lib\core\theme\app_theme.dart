import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Modern Professional Color Palette
  // Primary Colors - Blue gradient for trust and professionalism
  static const Color primaryColor = Color(0xFF1E3A8A); // Deep blue
  static const Color primaryLightColor = Color(0xFF3B82F6); // Bright blue
  static const Color primaryDarkColor = Color(0xFF1E40AF); // Darker blue

  // Secondary Colors - Complementary teal for accents
  static const Color secondaryColor = Color(0xFF0891B2); // Teal
  static const Color secondaryLightColor = Color(0xFF06B6D4); // Light teal
  static const Color secondaryDarkColor = Color(0xFF0E7490); // Dark teal

  // Accent Colors - Orange for CTAs and highlights
  static const Color accentColor = Color(0xFFEA580C); // Orange
  static const Color accentLightColor = Color(0xFFF97316); // Light orange
  static const Color accentDarkColor = Color(0x00dc2626); // Red-orange

  // Neutral Colors - Modern grayscale
  static const Color backgroundColor = Color(0xFFF8FAFC); // Very light gray
  static const Color surfaceColor = Color(0xFFFFFFFF); // Pure white
  static const Color surfaceVariantColor = Color(0xFFF1F5F9); // Light gray
  static const Color outlineColor = Color(0xFFE2E8F0); // Border gray
  static const Color outlineVariantColor = Color(0xFFCBD5E1); // Darker border

  // Text Colors - High contrast for accessibility
  static const Color textPrimaryColor = Color(0xFF0F172A); // Almost black
  static const Color textSecondaryColor = Color(0xFF475569); // Medium gray
  static const Color textTertiaryColor = Color(0xFF94A3B8); // Light gray
  static const Color textOnPrimaryColor = Color(0xFFFFFFFF); // White on primary

  // Semantic Colors - Status and feedback
  static const Color successColor = Color(0xFF059669); // Green
  static const Color successLightColor = Color(0xFF10B981); // Light green
  static const Color warningColor = Color(0xFFD97706); // Amber
  static const Color warningLightColor = Color(0xFFF59E0B); // Light amber
  static const Color errorColor = Color(0xFFDC2626); // Red
  static const Color errorLightColor = Color(0xFFEF4444); // Light red

  // Dark Theme Colors
  static const Color darkBackgroundColor = Color(0xFF0F172A); // Dark slate
  static const Color darkSurfaceColor = Color(0xFF1E293B); // Dark gray
  static const Color darkSurfaceVariantColor = Color(0xFF334155); // Medium dark
  static const Color darkTextPrimaryColor = Color(0xFFF8FAFC); // Light text
  static const Color darkTextSecondaryColor = Color(
    0xFFCBD5E1,
  ); // Medium light text

  // Design Tokens - Spacing System (4dp grid)
  static const double spacing4 = 4.0;
  static const double spacing8 = 8.0;
  static const double spacing12 = 12.0;
  static const double spacing16 = 16.0;
  static const double spacing20 = 20.0;
  static const double spacing24 = 24.0;
  static const double spacing32 = 32.0;
  static const double spacing40 = 40.0;
  static const double spacing48 = 48.0;
  static const double spacing64 = 64.0;

  // Border Radius System
  static const double radiusXS = 4.0; // Small elements
  static const double radiusSM = 8.0; // Buttons, chips
  static const double radiusMD = 12.0; // Cards, inputs
  static const double radiusLG = 16.0; // Large cards
  static const double radiusXL = 24.0; // Modals, sheets
  static const double radiusRound = 999.0; // Fully rounded

  // Elevation System
  static const double elevation0 = 0.0; // Flat elements
  static const double elevation1 = 1.0; // Subtle depth
  static const double elevation2 = 2.0; // Cards
  static const double elevation4 = 4.0; // Buttons, FABs
  static const double elevation8 = 8.0; // Modals
  static const double elevation16 = 16.0; // Navigation

  // Typography Scale
  static const double fontSizeXS = 12.0;
  static const double fontSizeSM = 14.0;
  static const double fontSizeBase = 16.0;
  static const double fontSizeLG = 18.0;
  static const double fontSizeXL = 20.0;
  static const double fontSize2XL = 24.0;
  static const double fontSize3XL = 30.0;
  static const double fontSize4XL = 36.0;

  // Line Heights
  static const double lineHeightTight = 1.25;
  static const double lineHeightNormal = 1.5;
  static const double lineHeightRelaxed = 1.75;

  // Light Theme
  static ThemeData get lightTheme {
    final textTheme = GoogleFonts.interTextTheme().copyWith(
      // Display styles
      displayLarge: GoogleFonts.inter(
        fontSize: fontSize4XL,
        fontWeight: FontWeight.w700,
        height: lineHeightTight,
        color: textPrimaryColor,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: fontSize3XL,
        fontWeight: FontWeight.w600,
        height: lineHeightTight,
        color: textPrimaryColor,
      ),
      // Headline styles
      headlineLarge: GoogleFonts.inter(
        fontSize: fontSize2XL,
        fontWeight: FontWeight.w600,
        height: lineHeightTight,
        color: textPrimaryColor,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: fontSizeXL,
        fontWeight: FontWeight.w600,
        height: lineHeightNormal,
        color: textPrimaryColor,
      ),
      // Title styles
      titleLarge: GoogleFonts.inter(
        fontSize: fontSizeLG,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: textPrimaryColor,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: fontSizeBase,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: textPrimaryColor,
      ),
      // Body styles
      bodyLarge: GoogleFonts.inter(
        fontSize: fontSizeBase,
        fontWeight: FontWeight.w400,
        height: lineHeightRelaxed,
        color: textPrimaryColor,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: fontSizeSM,
        fontWeight: FontWeight.w400,
        height: lineHeightNormal,
        color: textSecondaryColor,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: fontSizeXS,
        fontWeight: FontWeight.w400,
        height: lineHeightNormal,
        color: textTertiaryColor,
      ),
      // Label styles
      labelLarge: GoogleFonts.inter(
        fontSize: fontSizeSM,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: textPrimaryColor,
      ),
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.light(
        primary: primaryColor,
        onPrimary: textOnPrimaryColor,
        primaryContainer: primaryLightColor,
        onPrimaryContainer: textOnPrimaryColor,
        secondary: secondaryColor,
        onSecondary: textOnPrimaryColor,
        tertiary: accentColor,
        onTertiary: textOnPrimaryColor,
        error: errorColor,
        onError: textOnPrimaryColor,
        surface: surfaceColor,
        onSurface: textPrimaryColor,
        surfaceContainerHighest: surfaceVariantColor,
        onSurfaceVariant: textSecondaryColor,
        outline: outlineColor,
      ),
      textTheme: textTheme,
      scaffoldBackgroundColor: backgroundColor,
      appBarTheme: AppBarTheme(
        elevation: elevation0,
        centerTitle: true,
        backgroundColor: surfaceColor,
        foregroundColor: textPrimaryColor,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: textTheme.titleLarge,
        toolbarHeight: 64,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: elevation2,
          padding: const EdgeInsets.symmetric(
            horizontal: spacing24,
            vertical: spacing12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusSM),
          ),
          textStyle: textTheme.labelLarge,
        ),
      ),
      cardTheme: CardTheme(
        elevation: elevation2,
        color: surfaceColor,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMD),
        ),
        margin: const EdgeInsets.all(spacing8),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: outlineColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: outlineColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: primaryColor, width: 2),
        ),
        filled: true,
        fillColor: surfaceColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacing16,
          vertical: spacing12,
        ),
      ),
    );
  }

  // Dark Theme
  static ThemeData get darkTheme {
    final darkTextTheme = GoogleFonts.interTextTheme(
      ThemeData.dark().textTheme,
    ).copyWith(
      // Display styles
      displayLarge: GoogleFonts.inter(
        fontSize: fontSize4XL,
        fontWeight: FontWeight.w700,
        height: lineHeightTight,
        color: darkTextPrimaryColor,
      ),
      displayMedium: GoogleFonts.inter(
        fontSize: fontSize3XL,
        fontWeight: FontWeight.w600,
        height: lineHeightTight,
        color: darkTextPrimaryColor,
      ),
      // Headline styles
      headlineLarge: GoogleFonts.inter(
        fontSize: fontSize2XL,
        fontWeight: FontWeight.w600,
        height: lineHeightTight,
        color: darkTextPrimaryColor,
      ),
      headlineMedium: GoogleFonts.inter(
        fontSize: fontSizeXL,
        fontWeight: FontWeight.w600,
        height: lineHeightNormal,
        color: darkTextPrimaryColor,
      ),
      // Title styles
      titleLarge: GoogleFonts.inter(
        fontSize: fontSizeLG,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: darkTextPrimaryColor,
      ),
      titleMedium: GoogleFonts.inter(
        fontSize: fontSizeBase,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: darkTextPrimaryColor,
      ),
      // Body styles
      bodyLarge: GoogleFonts.inter(
        fontSize: fontSizeBase,
        fontWeight: FontWeight.w400,
        height: lineHeightRelaxed,
        color: darkTextPrimaryColor,
      ),
      bodyMedium: GoogleFonts.inter(
        fontSize: fontSizeSM,
        fontWeight: FontWeight.w400,
        height: lineHeightNormal,
        color: darkTextSecondaryColor,
      ),
      bodySmall: GoogleFonts.inter(
        fontSize: fontSizeXS,
        fontWeight: FontWeight.w400,
        height: lineHeightNormal,
        color: darkTextSecondaryColor,
      ),
      // Label styles
      labelLarge: GoogleFonts.inter(
        fontSize: fontSizeSM,
        fontWeight: FontWeight.w500,
        height: lineHeightNormal,
        color: darkTextPrimaryColor,
      ),
    );

    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.dark(
        primary: primaryLightColor,
        onPrimary: textPrimaryColor,
        primaryContainer: primaryColor,
        onPrimaryContainer: darkTextPrimaryColor,
        secondary: secondaryLightColor,
        onSecondary: textPrimaryColor,
        tertiary: accentLightColor,
        onTertiary: textPrimaryColor,
        error: errorLightColor,
        onError: textPrimaryColor,
        surface: darkSurfaceColor,
        onSurface: darkTextPrimaryColor,
        surfaceContainerHighest: darkSurfaceVariantColor,
        onSurfaceVariant: darkTextSecondaryColor,
        outline: outlineVariantColor,
      ),
      textTheme: darkTextTheme,
      scaffoldBackgroundColor: darkBackgroundColor,
      appBarTheme: AppBarTheme(
        elevation: elevation0,
        centerTitle: true,
        backgroundColor: darkSurfaceColor,
        foregroundColor: darkTextPrimaryColor,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: darkTextTheme.titleLarge,
        toolbarHeight: 64,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: elevation2,
          padding: const EdgeInsets.symmetric(
            horizontal: spacing24,
            vertical: spacing12,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radiusSM),
          ),
          textStyle: darkTextTheme.labelLarge,
        ),
      ),
      cardTheme: CardTheme(
        elevation: elevation2,
        color: darkSurfaceColor,
        surfaceTintColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMD),
        ),
        margin: const EdgeInsets.all(spacing8),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: outlineVariantColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: outlineVariantColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusSM),
          borderSide: const BorderSide(color: primaryLightColor, width: 2),
        ),
        filled: true,
        fillColor: darkSurfaceColor,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: spacing16,
          vertical: spacing12,
        ),
      ),
    );
  }
}
