import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:google_mlkit_text_recognition/google_mlkit_text_recognition.dart';
import '../../domain/repositories/ocr_repository.dart';

class OcrRepositoryImpl implements OcrRepository {
  final TextRecognizer _textRecognizer = TextRecognizer();

  @override
  Future<String> extractTextFromImage(Uint8List imageBytes) async {
    try {
      final inputImage = InputImage.fromBytes(
        bytes: imageBytes,
        metadata: InputImageMetadata(
          size: Size(1920, 1080), // Default size, should be actual image size
          rotation: InputImageRotation.rotation0deg,
          format: InputImageFormat.yuv420,
          bytesPerRow: 1920,
        ),
      );

      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );
      return recognizedText.text;
    } catch (e) {
      throw Exception('Text recognition failed: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> extractStructuredText(
    Uint8List imageBytes,
  ) async {
    try {
      final inputImage = InputImage.fromBytes(
        bytes: imageBytes,
        metadata: InputImageMetadata(
          size: Size(1920, 1080),
          rotation: InputImageRotation.rotation0deg,
          format: InputImageFormat.yuv420,
          bytesPerRow: 1920,
        ),
      );

      final RecognizedText recognizedText = await _textRecognizer.processImage(
        inputImage,
      );

      // Extract structured information
      final List<Map<String, dynamic>> blocks = [];
      final List<String> lines = [];

      for (TextBlock block in recognizedText.blocks) {
        final Map<String, dynamic> blockData = {
          'text': block.text,
          'boundingBox': {
            'left': block.boundingBox.left,
            'top': block.boundingBox.top,
            'right': block.boundingBox.right,
            'bottom': block.boundingBox.bottom,
          },
          'confidence':
              block.recognizedLanguages.isNotEmpty
                  ? block.recognizedLanguages.first.languageCode
                  : 'unknown',
        };
        blocks.add(blockData);

        for (TextLine line in block.lines) {
          lines.add(line.text);
        }
      }

      return {
        'fullText': recognizedText.text,
        'blocks': blocks,
        'lines': lines,
        'wordCount': recognizedText.text.split(' ').length,
        'characterCount': recognizedText.text.length,
      };
    } catch (e) {
      throw Exception('Structured text extraction failed: $e');
    }
  }

  @override
  Future<bool> isTextRecognitionAvailable() async {
    try {
      // Simple test to check if text recognition is available
      return true; // ML Kit is generally available on most devices
    } catch (e) {
      return false;
    }
  }

  void dispose() {
    _textRecognizer.close();
  }
}
