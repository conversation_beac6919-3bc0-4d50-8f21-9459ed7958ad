import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'core/di/injection_container.dart' as di;
import 'core/theme/app_theme.dart';
import 'features/home/<USER>/pages/home_screen.dart';
import 'features/camera/presentation/pages/camera_screen.dart';
import 'features/document_processing/presentation/pages/document_processing_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await di.init();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  runApp(const LensDocApp());
}

class LensDocApp extends StatelessWidget {
  const LensDocApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'LensDoc',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system,
      home: const HomeScreen(),
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/':
            return MaterialPageRoute(builder: (context) => const HomeScreen());
          case '/camera':
            return MaterialPageRoute(
              builder: (context) => const CameraScreen(),
            );
          case '/document-processing':
            final imageBytes = settings.arguments as Uint8List;
            return MaterialPageRoute(
              builder:
                  (context) => DocumentProcessingScreen(imageBytes: imageBytes),
            );
          default:
            return MaterialPageRoute(builder: (context) => const HomeScreen());
        }
      },
      debugShowCheckedModeBanner: false,
    );
  }
}
