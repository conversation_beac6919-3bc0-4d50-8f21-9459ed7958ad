import 'package:flutter/material.dart';

class CameraControls extends StatelessWidget {
  final VoidCallback onCapture;
  final bool isCapturing;

  const CameraControls({
    super.key,
    required this.onCapture,
    this.isCapturing = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Gallery Button
          IconButton(
            onPressed: isCapturing ? null : () {
              // TODO: Implement gallery picker
            },
            icon: const Icon(
              Icons.photo_library,
              color: Colors.white,
              size: 32,
            ),
          ),
          
          // Capture Button
          GestureDetector(
            onTap: isCapturing ? null : onCapture,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: Colors.white,
                  width: 4,
                ),
              ),
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isCapturing ? Colors.red : Colors.white,
                ),
                child: isCapturing
                    ? const Center(
                        child: SizedB<PERSON>(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        ),
                      )
                    : null,
              ),
            ),
          ),
          
          // Settings Button
          IconButton(
            onPressed: isCapturing ? null : () {
              // TODO: Implement camera settings
            },
            icon: const Icon(
              Icons.settings,
              color: Colors.white,
              size: 32,
            ),
          ),
        ],
      ),
    );
  }
}
